import React from 'react';
import { View, StyleSheet } from 'react-native';
import {
  Card,
  Title,
  Text,
  List,
  Avatar,
  Chip,
  Button,
  Divider,
} from 'react-native-paper';

export default function TripSummaryCard({ balances, settlements, onSettlementPress }) {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((word) => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getBalanceColor = (balance) => {
    if (balance > 0.01) return '#4CAF50'; // Green for positive
    if (balance < -0.01) return '#F44336'; // Red for negative
    return '#757575'; // Gray for zero
  };

  const getBalanceText = (balance) => {
    if (balance > 0.01) return 'is owed';
    if (balance < -0.01) return 'owes';
    return 'is settled';
  };

  const sortedBalances = Object.values(balances || {}).sort((a, b) => b.netBalance - a.netBalance);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Title>Trip Summary</Title>

        {/* Balances Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Individual Balances</Text>
          
          {sortedBalances.map((balance) => (
            <List.Item
              key={balance.userId}
              title={balance.name}
              description={`Paid ${formatCurrency(balance.totalPaid)} • Owes ${formatCurrency(balance.totalOwed)}`}
              left={() => (
                <Avatar.Text
                  size={40}
                  label={getInitials(balance.name)}
                />
              )}
              right={() => (
                <View style={styles.balanceRight}>
                  <Text style={styles.balanceLabel}>
                    {getBalanceText(balance.netBalance)}
                  </Text>
                  <Text style={[
                    styles.balanceAmount,
                    { color: getBalanceColor(balance.netBalance) }
                  ]}>
                    {formatCurrency(Math.abs(balance.netBalance))}
                  </Text>
                </View>
              )}
            />
          ))}
        </View>

        {/* Settlements Section */}
        {settlements && settlements.length > 0 && (
          <>
            <Divider style={styles.divider} />
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Suggested Settlements</Text>
              
              {settlements.map((settlement, index) => (
                <View key={index} style={styles.settlementItem}>
                  <View style={styles.settlementInfo}>
                    <View style={styles.settlementUsers}>
                      <Avatar.Text
                        size={32}
                        label={getInitials(settlement.fromUserName)}
                      />
                      <Text style={styles.settlementArrow}>→</Text>
                      <Avatar.Text
                        size={32}
                        label={getInitials(settlement.toUserName)}
                      />
                    </View>
                    
                    <View style={styles.settlementDetails}>
                      <Text style={styles.settlementText}>
                        {settlement.fromUserName} pays {settlement.toUserName}
                      </Text>
                      <Text style={styles.settlementAmount}>
                        {formatCurrency(settlement.amount)}
                      </Text>
                    </View>
                  </View>
                  
                  {onSettlementPress && (
                    <Button
                      mode="outlined"
                      compact
                      onPress={() => onSettlementPress(settlement)}
                      style={styles.settlementButton}
                    >
                      Mark Paid
                    </Button>
                  )}
                </View>
              ))}
            </View>
          </>
        )}

        {/* No settlements needed */}
        {settlements && settlements.length === 0 && (
          <>
            <Divider style={styles.divider} />
            <View style={styles.section}>
              <View style={styles.settledState}>
                <Chip icon="check-circle" mode="outlined" style={styles.settledChip}>
                  All Settled!
                </Chip>
                <Text style={styles.settledText}>
                  Everyone is settled up. No payments needed!
                </Text>
              </View>
            </View>
          </>
        )}
      </Card.Content>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    elevation: 2,
    marginBottom: 16,
  },
  section: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  balanceRight: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  balanceLabel: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 2,
  },
  balanceAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  divider: {
    marginTop: 16,
  },
  settlementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 8,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    marginBottom: 8,
  },
  settlementInfo: {
    flex: 1,
  },
  settlementUsers: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  settlementArrow: {
    fontSize: 18,
    marginHorizontal: 12,
    color: '#666',
  },
  settlementDetails: {
    marginLeft: 8,
  },
  settlementText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  settlementAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  settlementButton: {
    marginLeft: 12,
  },
  settledState: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  settledChip: {
    marginBottom: 12,
    borderColor: '#4CAF50',
  },
  settledText: {
    textAlign: 'center',
    opacity: 0.7,
    fontSize: 14,
  },
});
