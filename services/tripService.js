import { supabase } from '../lib/supabase';

export const tripService = {
  // Get all trips for the current user
  async getUserTrips() {
    try {
      const { data, error } = await supabase
        .from('trips')
        .select(`
          *,
          trip_members!inner(
            role,
            joined_at,
            profiles(id, full_name, email)
          ),
          expenses(
            id,
            total_amount,
            currency
          )
        `)
        .eq('trip_members.user_id', (await supabase.auth.getUser()).data.user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Create a new trip
  async createTrip(tripData) {
    try {
      const user = (await supabase.auth.getUser()).data.user;
      
      // Create the trip
      const { data: trip, error: tripError } = await supabase
        .from('trips')
        .insert({
          name: tripData.name,
          description: tripData.description,
          created_by: user.id,
        })
        .select()
        .single();

      if (tripError) throw tripError;

      // Add the creator as owner
      const { error: memberError } = await supabase
        .from('trip_members')
        .insert({
          trip_id: trip.id,
          user_id: user.id,
          role: 'owner',
        });

      if (memberError) throw memberError;

      return { data: trip, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get trip details with members and expenses
  async getTripDetails(tripId) {
    try {
      const { data, error } = await supabase
        .from('trips')
        .select(`
          *,
          trip_members(
            id,
            role,
            joined_at,
            profiles(id, full_name, email, avatar_url)
          ),
          expenses(
            id,
            title,
            description,
            total_amount,
            currency,
            created_at,
            paid_by,
            profiles!expenses_paid_by_fkey(full_name),
            expense_items(
              id,
              name,
              amount,
              is_shared
            )
          )
        `)
        .eq('id', tripId)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Add member to trip by email
  async addMemberToTrip(tripId, email) {
    try {
      // First, find the user by email
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single();

      if (profileError) {
        if (profileError.code === 'PGRST116') {
          throw new Error('User not found with this email');
        }
        throw profileError;
      }

      // Check if user is already a member
      const { data: existingMember, error: checkError } = await supabase
        .from('trip_members')
        .select('id')
        .eq('trip_id', tripId)
        .eq('user_id', profile.id)
        .single();

      if (existingMember) {
        throw new Error('User is already a member of this trip');
      }

      // Add the member
      const { data, error } = await supabase
        .from('trip_members')
        .insert({
          trip_id: tripId,
          user_id: profile.id,
          role: 'member',
        })
        .select(`
          id,
          role,
          joined_at,
          profiles(id, full_name, email, avatar_url)
        `)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Remove member from trip
  async removeMemberFromTrip(tripId, userId) {
    try {
      const { error } = await supabase
        .from('trip_members')
        .delete()
        .eq('trip_id', tripId)
        .eq('user_id', userId);

      if (error) throw error;
      return { error: null };
    } catch (error) {
      return { error };
    }
  },

  // Update trip
  async updateTrip(tripId, updates) {
    try {
      const { data, error } = await supabase
        .from('trips')
        .update(updates)
        .eq('id', tripId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Delete trip
  async deleteTrip(tripId) {
    try {
      const { error } = await supabase
        .from('trips')
        .delete()
        .eq('id', tripId);

      if (error) throw error;
      return { error: null };
    } catch (error) {
      return { error };
    }
  },

  // Get trip balance summary
  async getTripBalances(tripId) {
    try {
      // Get all expenses with their splits and member info
      const { data: expenses, error: expensesError } = await supabase
        .from('expenses')
        .select(`
          id,
          total_amount,
          paid_by,
          expense_items(
            id,
            amount,
            expense_splits(
              user_id,
              amount
            )
          )
        `)
        .eq('trip_id', tripId);

      if (expensesError) throw expensesError;

      // Get trip members
      const { data: members, error: membersError } = await supabase
        .from('trip_members')
        .select(`
          user_id,
          profiles(id, full_name)
        `)
        .eq('trip_id', tripId);

      if (membersError) throw membersError;

      // Transform data for splitting service
      const transformedExpenses = expenses.map(expense => ({
        id: expense.id,
        totalAmount: parseFloat(expense.total_amount),
        paidBy: expense.paid_by,
        items: expense.expense_items.map(item => ({
          id: item.id,
          amount: parseFloat(item.amount),
          splits: item.expense_splits.map(split => ({
            userId: split.user_id,
            amount: parseFloat(split.amount),
          })),
        })),
      }));

      const transformedMembers = members.map(member => ({
        id: member.user_id,
        name: member.profiles?.full_name || 'Unknown',
      }));

      // Use splitting service to calculate balances
      const { splittingService } = await import('./splittingService');
      const balances = splittingService.calculateTripBalances(transformedExpenses, transformedMembers);
      const settlements = splittingService.calculateSettlements(balances);
      const optimizedSettlements = splittingService.optimizeSettlements(settlements);

      return {
        data: {
          balances,
          settlements: optimizedSettlements,
        },
        error: null
      };
    } catch (error) {
      return { data: null, error };
    }
  },
};
