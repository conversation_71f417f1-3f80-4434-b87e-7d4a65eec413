// Service for handling complex expense splitting logic

export const splittingService = {
  // Calculate even splits for an amount among multiple users
  calculateEvenSplit(amount, userIds) {
    if (!userIds || userIds.length === 0) {
      return [];
    }

    const splitAmount = amount / userIds.length;
    return userIds.map(userId => ({
      userId,
      amount: Math.round(splitAmount * 100) / 100, // Round to 2 decimal places
    }));
  },

  // Calculate custom splits based on percentages
  calculatePercentageSplit(amount, splits) {
    const totalPercentage = splits.reduce((sum, split) => sum + split.percentage, 0);
    
    if (Math.abs(totalPercentage - 100) > 0.01) {
      throw new Error('Percentages must add up to 100%');
    }

    return splits.map(split => ({
      userId: split.userId,
      amount: Math.round((amount * split.percentage / 100) * 100) / 100,
    }));
  },

  // Calculate custom splits based on exact amounts
  calculateExactSplit(totalAmount, splits) {
    const splitTotal = splits.reduce((sum, split) => sum + split.amount, 0);
    
    if (Math.abs(splitTotal - totalAmount) > 0.01) {
      throw new Error('Split amounts must equal the total amount');
    }

    return splits.map(split => ({
      userId: split.userId,
      amount: Math.round(split.amount * 100) / 100,
    }));
  },

  // Calculate balances for a trip based on all expenses
  calculateTripBalances(expenses, members) {
    const balances = {};
    
    // Initialize balances for all members
    members.forEach(member => {
      balances[member.id] = {
        userId: member.id,
        name: member.name,
        totalPaid: 0,
        totalOwed: 0,
        netBalance: 0,
      };
    });

    // Process each expense
    expenses.forEach(expense => {
      // Add what the payer paid
      if (balances[expense.paidBy]) {
        balances[expense.paidBy].totalPaid += expense.totalAmount;
      }

      // Process each expense item
      expense.items.forEach(item => {
        item.splits.forEach(split => {
          if (balances[split.userId]) {
            balances[split.userId].totalOwed += split.amount;
          }
        });
      });
    });

    // Calculate net balances
    Object.keys(balances).forEach(userId => {
      const balance = balances[userId];
      balance.netBalance = balance.totalPaid - balance.totalOwed;
    });

    return balances;
  },

  // Calculate who owes whom and how much
  calculateSettlements(balances) {
    const creditors = []; // People who are owed money (positive balance)
    const debtors = []; // People who owe money (negative balance)

    // Separate creditors and debtors
    Object.values(balances).forEach(balance => {
      if (balance.netBalance > 0.01) {
        creditors.push({
          userId: balance.userId,
          name: balance.name,
          amount: balance.netBalance,
        });
      } else if (balance.netBalance < -0.01) {
        debtors.push({
          userId: balance.userId,
          name: balance.name,
          amount: Math.abs(balance.netBalance),
        });
      }
    });

    const settlements = [];

    // Sort by amount (largest first) for optimal settlement
    creditors.sort((a, b) => b.amount - a.amount);
    debtors.sort((a, b) => b.amount - a.amount);

    // Create copies to avoid modifying original arrays
    const remainingCreditors = [...creditors];
    const remainingDebtors = [...debtors];

    // Calculate settlements using greedy algorithm
    while (remainingCreditors.length > 0 && remainingDebtors.length > 0) {
      const creditor = remainingCreditors[0];
      const debtor = remainingDebtors[0];

      const settlementAmount = Math.min(creditor.amount, debtor.amount);

      if (settlementAmount > 0.01) {
        settlements.push({
          fromUserId: debtor.userId,
          fromUserName: debtor.name,
          toUserId: creditor.userId,
          toUserName: creditor.name,
          amount: Math.round(settlementAmount * 100) / 100,
        });

        // Update remaining amounts
        creditor.amount -= settlementAmount;
        debtor.amount -= settlementAmount;
      }

      // Remove settled parties
      if (creditor.amount <= 0.01) {
        remainingCreditors.shift();
      }
      if (debtor.amount <= 0.01) {
        remainingDebtors.shift();
      }
    }

    return settlements;
  },

  // Optimize settlements to minimize number of transactions
  optimizeSettlements(settlements) {
    // Group settlements by user pairs
    const settlementMap = new Map();

    settlements.forEach(settlement => {
      const key1 = `${settlement.fromUserId}-${settlement.toUserId}`;
      const key2 = `${settlement.toUserId}-${settlement.fromUserId}`;

      if (settlementMap.has(key2)) {
        // There's a reverse settlement, net them out
        const existing = settlementMap.get(key2);
        const netAmount = existing.amount - settlement.amount;

        if (Math.abs(netAmount) > 0.01) {
          if (netAmount > 0) {
            // Keep the existing settlement with reduced amount
            existing.amount = netAmount;
          } else {
            // Replace with reverse settlement
            settlementMap.delete(key2);
            settlementMap.set(key1, {
              ...settlement,
              amount: Math.abs(netAmount),
            });
          }
        } else {
          // They cancel out completely
          settlementMap.delete(key2);
        }
      } else {
        settlementMap.set(key1, settlement);
      }
    });

    return Array.from(settlementMap.values());
  },

  // Validate expense splits
  validateExpenseSplits(expense) {
    const errors = [];

    if (!expense.items || expense.items.length === 0) {
      errors.push('Expense must have at least one item');
      return errors;
    }

    let totalItemAmount = 0;

    expense.items.forEach((item, itemIndex) => {
      if (!item.name || item.name.trim().length === 0) {
        errors.push(`Item ${itemIndex + 1}: Name is required`);
      }

      if (!item.amount || item.amount <= 0) {
        errors.push(`Item ${itemIndex + 1}: Amount must be greater than 0`);
      } else {
        totalItemAmount += item.amount;
      }

      if (!item.splits || item.splits.length === 0) {
        errors.push(`Item ${itemIndex + 1}: Must be assigned to at least one person`);
      } else {
        const splitTotal = item.splits.reduce((sum, split) => sum + split.amount, 0);
        if (Math.abs(splitTotal - item.amount) > 0.01) {
          errors.push(`Item ${itemIndex + 1}: Split amounts (${splitTotal}) don't match item amount (${item.amount})`);
        }
      }
    });

    // Check if items total matches expense total
    if (Math.abs(totalItemAmount - expense.totalAmount) > 0.01) {
      errors.push(`Items total (${totalItemAmount}) doesn't match expense total (${expense.totalAmount})`);
    }

    return errors;
  },

  // Generate split suggestions based on expense patterns
  suggestSplits(expense, members, previousExpenses = []) {
    const suggestions = [];

    // Analyze previous expenses for patterns
    const memberFrequency = {};
    const memberPreferences = {};

    members.forEach(member => {
      memberFrequency[member.id] = 0;
      memberPreferences[member.id] = { shared: 0, individual: 0 };
    });

    previousExpenses.forEach(prevExpense => {
      prevExpense.items.forEach(item => {
        item.splits.forEach(split => {
          if (memberFrequency[split.userId] !== undefined) {
            memberFrequency[split.userId]++;
            if (item.isShared) {
              memberPreferences[split.userId].shared++;
            } else {
              memberPreferences[split.userId].individual++;
            }
          }
        });
      });
    });

    // Generate suggestions for each item
    expense.items.forEach((item, index) => {
      const itemSuggestions = [];

      // Suggestion 1: Split evenly among all members
      itemSuggestions.push({
        type: 'even_all',
        description: 'Split evenly among all members',
        splits: this.calculateEvenSplit(item.amount, members.map(m => m.id)),
      });

      // Suggestion 2: Split evenly among frequent participants
      const frequentMembers = members
        .filter(member => memberFrequency[member.id] > previousExpenses.length * 0.5)
        .map(m => m.id);

      if (frequentMembers.length > 0 && frequentMembers.length < members.length) {
        itemSuggestions.push({
          type: 'even_frequent',
          description: 'Split among frequent participants',
          splits: this.calculateEvenSplit(item.amount, frequentMembers),
        });
      }

      // Suggestion 3: Assign to payer only
      itemSuggestions.push({
        type: 'payer_only',
        description: 'Assign to payer only',
        splits: [{
          userId: expense.paidBy,
          amount: item.amount,
        }],
      });

      suggestions.push({
        itemIndex: index,
        itemName: item.name,
        suggestions: itemSuggestions,
      });
    });

    return suggestions;
  },

  // Format currency for display
  formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  },

  // Round amount to avoid floating point precision issues
  roundAmount(amount) {
    return Math.round(amount * 100) / 100;
  },
};
