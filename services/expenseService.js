import { supabase } from '../lib/supabase';

export const expenseService = {
  // Create a new expense with items and splits
  async createExpense(expenseData) {
    try {
      const user = (await supabase.auth.getUser()).data.user;
      
      // Create the expense
      const { data: expense, error: expenseError } = await supabase
        .from('expenses')
        .insert({
          trip_id: expenseData.tripId,
          title: expenseData.title,
          description: expenseData.description,
          total_amount: expenseData.totalAmount,
          currency: expenseData.currency || 'USD',
          paid_by: expenseData.paidBy || user.id,
          receipt_url: expenseData.receiptUrl,
        })
        .select()
        .single();

      if (expenseError) throw expenseError;

      // Create expense items and their splits
      for (const item of expenseData.items) {
        const { data: expenseItem, error: itemError } = await supabase
          .from('expense_items')
          .insert({
            expense_id: expense.id,
            name: item.name,
            amount: item.amount,
            quantity: item.quantity || 1,
            is_shared: item.isShared,
          })
          .select()
          .single();

        if (itemError) throw itemError;

        // Create splits for this item
        if (item.splits && item.splits.length > 0) {
          const splits = item.splits.map(split => ({
            expense_item_id: expenseItem.id,
            user_id: split.userId,
            amount: split.amount,
          }));

          const { error: splitsError } = await supabase
            .from('expense_splits')
            .insert(splits);

          if (splitsError) throw splitsError;
        }
      }

      return { data: expense, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get expense details with items and splits
  async getExpenseDetails(expenseId) {
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select(`
          *,
          profiles!expenses_paid_by_fkey(id, full_name, email),
          trips(id, name),
          expense_items(
            id,
            name,
            amount,
            quantity,
            is_shared,
            expense_splits(
              id,
              amount,
              profiles(id, full_name, email)
            )
          )
        `)
        .eq('id', expenseId)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Update expense
  async updateExpense(expenseId, updates) {
    try {
      const { data, error } = await supabase
        .from('expenses')
        .update(updates)
        .eq('id', expenseId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Delete expense
  async deleteExpense(expenseId) {
    try {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('id', expenseId);

      if (error) throw error;
      return { error: null };
    } catch (error) {
      return { error };
    }
  },

  // Get expenses for a trip
  async getTripExpenses(tripId) {
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select(`
          *,
          profiles!expenses_paid_by_fkey(id, full_name, email),
          expense_items(
            id,
            name,
            amount,
            quantity,
            is_shared
          )
        `)
        .eq('trip_id', tripId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Calculate splits for shared items
  calculateEvenSplits(amount, memberIds) {
    const splitAmount = amount / memberIds.length;
    return memberIds.map(userId => ({
      userId,
      amount: splitAmount,
    }));
  },

  // Validate expense data
  validateExpenseData(expenseData) {
    const errors = [];

    if (!expenseData.title || expenseData.title.trim().length === 0) {
      errors.push('Title is required');
    }

    if (!expenseData.totalAmount || expenseData.totalAmount <= 0) {
      errors.push('Total amount must be greater than 0');
    }

    if (!expenseData.items || expenseData.items.length === 0) {
      errors.push('At least one item is required');
    }

    // Validate items
    expenseData.items?.forEach((item, index) => {
      if (!item.name || item.name.trim().length === 0) {
        errors.push(`Item ${index + 1}: Name is required`);
      }

      if (!item.amount || item.amount <= 0) {
        errors.push(`Item ${index + 1}: Amount must be greater than 0`);
      }

      if (!item.splits || item.splits.length === 0) {
        errors.push(`Item ${index + 1}: At least one person must be assigned`);
      }

      // Validate that splits add up to item amount
      const totalSplits = item.splits?.reduce((sum, split) => sum + split.amount, 0) || 0;
      if (Math.abs(totalSplits - item.amount) > 0.01) {
        errors.push(`Item ${index + 1}: Splits don't add up to item amount`);
      }
    });

    // Validate that items add up to total amount
    const itemsTotal = expenseData.items?.reduce((sum, item) => sum + item.amount, 0) || 0;
    if (Math.abs(itemsTotal - expenseData.totalAmount) > 0.01) {
      errors.push('Items total doesn\'t match expense total');
    }

    return errors;
  },
};
