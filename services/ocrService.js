// OCR Service for receipt scanning
// This is a mock implementation - in a real app you would integrate with services like:
// - Google Cloud Vision API
// - AWS Textract
// - Azure Computer Vision
// - Tesseract.js for client-side OCR

export const ocrService = {
  // Mock OCR function that simulates receipt text extraction
  async extractTextFromImage(imageUri) {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // <PERSON><PERSON> extracted text from a receipt
      const mockReceiptText = `
        RESTAURANT ABC
        123 Main Street
        City, State 12345
        
        Date: 2024-01-15
        Time: 19:30
        
        Table: 4
        Server: John
        
        ITEMS:
        Appetizer Platter    $18.99
        Caesar Salad         $12.50
        Grilled Salmon       $24.99
        Pasta Primavera      $16.99
        Chocolate Cake       $8.99
        
        Subtotal:           $82.46
        Tax (8.5%):         $7.01
        Tip (18%):          $14.84
        
        TOTAL:              $104.31
        
        Payment: Credit Card
        Card: ****1234
        
        Thank you for dining with us!
      `;
      
      return {
        success: true,
        text: mockReceiptText,
        confidence: 0.95,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        confidence: 0,
      };
    }
  },

  // Parse extracted text to identify receipt items and amounts
  parseReceiptText(text) {
    try {
      const lines = text.split('\n').map(line => line.trim()).filter(line => line);
      const items = [];
      let total = 0;
      let subtotal = 0;
      let tax = 0;
      let tip = 0;
      
      // Common patterns for receipt items
      const itemPatterns = [
        // Pattern: "Item Name    $12.99"
        /^(.+?)\s+\$(\d+\.\d{2})$/,
        // Pattern: "Item Name $12.99"
        /^(.+?)\s\$(\d+\.\d{2})$/,
        // Pattern: "Item Name    12.99"
        /^(.+?)\s+(\d+\.\d{2})$/,
      ];

      const totalPatterns = [
        /^TOTAL:?\s*\$?(\d+\.\d{2})$/i,
        /^Total\s*\$?(\d+\.\d{2})$/i,
        /^Grand Total:?\s*\$?(\d+\.\d{2})$/i,
      ];

      const subtotalPatterns = [
        /^SUBTOTAL:?\s*\$?(\d+\.\d{2})$/i,
        /^Subtotal\s*\$?(\d+\.\d{2})$/i,
        /^Sub Total:?\s*\$?(\d+\.\d{2})$/i,
      ];

      const taxPatterns = [
        /^TAX.*?:?\s*\$?(\d+\.\d{2})$/i,
        /^Tax.*?\s*\$?(\d+\.\d{2})$/i,
      ];

      const tipPatterns = [
        /^TIP.*?:?\s*\$?(\d+\.\d{2})$/i,
        /^Tip.*?\s*\$?(\d+\.\d{2})$/i,
        /^Gratuity.*?:?\s*\$?(\d+\.\d{2})$/i,
      ];

      // Skip common header/footer words
      const skipWords = [
        'restaurant', 'cafe', 'store', 'shop', 'market',
        'address', 'phone', 'tel', 'fax', 'email',
        'date', 'time', 'server', 'cashier', 'table',
        'receipt', 'invoice', 'bill', 'check',
        'thank', 'you', 'visit', 'again', 'welcome',
        'payment', 'card', 'cash', 'change',
        'order', 'number', '#'
      ];

      for (const line of lines) {
        // Check for total
        for (const pattern of totalPatterns) {
          const match = line.match(pattern);
          if (match) {
            total = parseFloat(match[1]);
            continue;
          }
        }

        // Check for subtotal
        for (const pattern of subtotalPatterns) {
          const match = line.match(pattern);
          if (match) {
            subtotal = parseFloat(match[1]);
            continue;
          }
        }

        // Check for tax
        for (const pattern of taxPatterns) {
          const match = line.match(pattern);
          if (match) {
            tax = parseFloat(match[1]);
            continue;
          }
        }

        // Check for tip
        for (const pattern of tipPatterns) {
          const match = line.match(pattern);
          if (match) {
            tip = parseFloat(match[1]);
            continue;
          }
        }

        // Check for items
        for (const pattern of itemPatterns) {
          const match = line.match(pattern);
          if (match) {
            const itemName = match[1].trim();
            const amount = parseFloat(match[2]);
            
            // Skip if item name contains skip words or is too short
            const lowerName = itemName.toLowerCase();
            const hasSkipWord = skipWords.some(word => lowerName.includes(word));
            
            if (!hasSkipWord && itemName.length > 2 && amount > 0) {
              items.push({
                name: itemName,
                amount: amount,
                quantity: 1,
                isShared: true, // Default to shared
              });
            }
            break;
          }
        }
      }

      // If no total found, calculate from items
      if (total === 0 && items.length > 0) {
        total = items.reduce((sum, item) => sum + item.amount, 0);
      }

      // If no subtotal found, use total minus tax and tip
      if (subtotal === 0 && total > 0) {
        subtotal = total - tax - tip;
      }

      return {
        success: true,
        items: items,
        totals: {
          subtotal: subtotal,
          tax: tax,
          tip: tip,
          total: total,
        },
        confidence: items.length > 0 ? 0.8 : 0.3,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        items: [],
        totals: {},
        confidence: 0,
      };
    }
  },

  // Complete OCR process: extract text and parse items
  async processReceiptImage(imageUri) {
    try {
      // Extract text from image
      const ocrResult = await this.extractTextFromImage(imageUri);
      
      if (!ocrResult.success) {
        return {
          success: false,
          error: ocrResult.error,
        };
      }

      // Parse the extracted text
      const parseResult = this.parseReceiptText(ocrResult.text);
      
      return {
        success: parseResult.success,
        items: parseResult.items,
        totals: parseResult.totals,
        rawText: ocrResult.text,
        confidence: Math.min(ocrResult.confidence, parseResult.confidence),
        error: parseResult.error,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        items: [],
        totals: {},
        confidence: 0,
      };
    }
  },

  // Validate and clean up parsed items
  validateAndCleanItems(items) {
    return items
      .filter(item => item.name && item.amount > 0)
      .map(item => ({
        ...item,
        name: item.name.replace(/[^\w\s-]/g, '').trim(), // Clean special characters
        amount: Math.round(item.amount * 100) / 100, // Round to 2 decimal places
        quantity: item.quantity || 1,
        isShared: item.isShared !== false, // Default to true
      }));
  },
};
