import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  Title,
  Paragraph,
  HelperText,
} from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import { useAuth } from '../hooks/useAuth';

export default function AuthScreen() {
  const [isSignUp, setIsSignUp] = useState(false);
  const [loading, setLoading] = useState(false);
  const { signIn, signUp, resetPassword } = useAuth();

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      fullName: '',
    },
  });

  const password = watch('password');

  const onSubmit = async (data) => {
    setLoading(true);
    try {
      if (isSignUp) {
        const { error } = await signUp(data.email, data.password, data.fullName);
        if (error) {
          Alert.alert('Sign Up Error', error.message);
        } else {
          Alert.alert(
            'Success',
            'Please check your email for verification link!'
          );
          setIsSignUp(false);
          reset();
        }
      } else {
        const { error } = await signIn(data.email, data.password);
        if (error) {
          Alert.alert('Sign In Error', error.message);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    const email = watch('email');
    if (!email) {
      Alert.alert('Error', 'Please enter your email address first');
      return;
    }

    setLoading(true);
    try {
      const { error } = await resetPassword(email);
      if (error) {
        Alert.alert('Error', error.message);
      } else {
        Alert.alert('Success', 'Password reset email sent!');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const toggleMode = () => {
    setIsSignUp(!isSignUp);
    reset();
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.content}>
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.title}>
                {isSignUp ? 'Create Account' : 'Welcome Back'}
              </Title>
              <Paragraph style={styles.subtitle}>
                {isSignUp
                  ? 'Sign up to start tracking expenses'
                  : 'Sign in to your account'}
              </Paragraph>

              <View style={styles.form}>
                {isSignUp && (
                  <>
                    <Controller
                      control={control}
                      name="fullName"
                      rules={{
                        required: 'Full name is required',
                        minLength: {
                          value: 2,
                          message: 'Name must be at least 2 characters',
                        },
                      }}
                      render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                          label="Full Name"
                          value={value}
                          onBlur={onBlur}
                          onChangeText={onChange}
                          error={!!errors.fullName}
                          style={styles.input}
                          mode="outlined"
                        />
                      )}
                    />
                    <HelperText type="error" visible={!!errors.fullName}>
                      {errors.fullName?.message}
                    </HelperText>
                  </>
                )}

                <Controller
                  control={control}
                  name="email"
                  rules={{
                    required: 'Email is required',
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: 'Invalid email address',
                    },
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Email"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.email}
                      style={styles.input}
                      mode="outlined"
                      keyboardType="email-address"
                      autoCapitalize="none"
                    />
                  )}
                />
                <HelperText type="error" visible={!!errors.email}>
                  {errors.email?.message}
                </HelperText>

                <Controller
                  control={control}
                  name="password"
                  rules={{
                    required: 'Password is required',
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters',
                    },
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Password"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.password}
                      style={styles.input}
                      mode="outlined"
                      secureTextEntry={true}
                      autoCapitalize="none"
                      autoCorrect={false}
                    />
                  )}
                />
                <HelperText type="error" visible={!!errors.password}>
                  {errors.password?.message}
                </HelperText>

                {isSignUp && (
                  <>
                    <Controller
                      control={control}
                      name="confirmPassword"
                      rules={{
                        required: 'Please confirm your password',
                        validate: (value) =>
                          value === password || 'Passwords do not match',
                      }}
                      render={({ field: { onChange, onBlur, value } }) => (
                        <TextInput
                          label="Confirm Password"
                          value={value}
                          onBlur={onBlur}
                          onChangeText={onChange}
                          error={!!errors.confirmPassword}
                          style={styles.input}
                          mode="outlined"
                          secureTextEntry={true}
                          autoCapitalize="none"
                          autoCorrect={false}
                        />
                      )}
                    />
                    <HelperText type="error" visible={!!errors.confirmPassword}>
                      {errors.confirmPassword?.message}
                    </HelperText>
                  </>
                )}

                <Button
                  mode="contained"
                  onPress={handleSubmit(onSubmit)}
                  loading={loading}
                  disabled={loading}
                  style={styles.submitButton}
                >
                  {isSignUp ? 'Sign Up' : 'Sign In'}
                </Button>

                {!isSignUp && (
                  <Button
                    mode="text"
                    onPress={handleForgotPassword}
                    disabled={loading}
                    style={styles.forgotButton}
                  >
                    Forgot Password?
                  </Button>
                )}

                <Button
                  mode="text"
                  onPress={toggleMode}
                  disabled={loading}
                  style={styles.toggleButton}
                >
                  {isSignUp
                    ? 'Already have an account? Sign In'
                    : "Don't have an account? Sign Up"}
                </Button>
              </View>
            </Card.Content>
          </Card>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  content: {
    padding: 20,
  },
  card: {
    elevation: 4,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 24,
    opacity: 0.7,
  },
  form: {
    gap: 8,
  },
  input: {
    marginBottom: 4,
  },
  submitButton: {
    marginTop: 16,
    marginBottom: 8,
  },
  forgotButton: {
    marginBottom: 8,
  },
  toggleButton: {
    marginTop: 8,
  },
});
