import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  TextInput,
  Button,
  Card,
  Title,
  Text,
  List,
  IconButton,
  Chip,
  Menu,
  Divider,
} from 'react-native-paper';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { tripService } from '../services/tripService';
import { expenseService } from '../services/expenseService';
import { useAuth } from '../hooks/useAuth';

export default function AddExpenseScreen({ route, navigation }) {
  const { tripId, scannedData } = route.params;
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [trip, setTrip] = useState(null);
  const [paidByMenuVisible, setPaidByMenuVisible] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      title: scannedData?.title || '',
      description: '',
      totalAmount: scannedData?.totalAmount?.toString() || '',
      currency: 'USD',
      paidBy: user?.id,
      items: scannedData?.items?.map(item => ({
        name: item.name,
        amount: item.amount.toString(),
        quantity: item.quantity,
        isShared: item.isShared,
        splits: [],
      })) || [
        {
          name: '',
          amount: '',
          quantity: 1,
          isShared: true,
          splits: [],
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items',
  });

  const watchedItems = watch('items');
  const watchedTotal = watch('totalAmount');

  useEffect(() => {
    loadTripDetails();
  }, []);

  useEffect(() => {
    // Auto-calculate total from items
    const itemsTotal = watchedItems.reduce((sum, item) => {
      return sum + (parseFloat(item.amount) || 0);
    }, 0);
    
    if (itemsTotal > 0 && itemsTotal !== parseFloat(watchedTotal)) {
      setValue('totalAmount', itemsTotal.toFixed(2));
    }
  }, [watchedItems, setValue, watchedTotal]);

  const loadTripDetails = async () => {
    try {
      const { data, error } = await tripService.getTripDetails(tripId);
      if (error) {
        Alert.alert('Error', 'Failed to load trip details');
      } else {
        setTrip(data);
        // Set default payer to current user
        setValue('paidBy', user?.id);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    }
  };

  const onSubmit = async (data) => {
    setLoading(true);
    try {
      // Prepare expense data
      const expenseData = {
        tripId,
        title: data.title,
        description: data.description,
        totalAmount: parseFloat(data.totalAmount),
        currency: data.currency,
        paidBy: data.paidBy,
        items: data.items.map(item => ({
          name: item.name,
          amount: parseFloat(item.amount),
          quantity: item.quantity,
          isShared: item.isShared,
          splits: item.isShared 
            ? expenseService.calculateEvenSplits(
                parseFloat(item.amount),
                trip.trip_members.map(member => member.profiles.id)
              )
            : item.splits,
        })),
      };

      // Validate expense data
      const validationErrors = expenseService.validateExpenseData(expenseData);
      if (validationErrors.length > 0) {
        Alert.alert('Validation Error', validationErrors.join('\n'));
        return;
      }

      const { data: expense, error } = await expenseService.createExpense(expenseData);
      
      if (error) {
        Alert.alert('Error', error.message);
      } else {
        Alert.alert(
          'Success',
          'Expense added successfully!',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const addItem = () => {
    append({
      name: '',
      amount: '',
      quantity: 1,
      isShared: true,
      splits: [],
    });
  };

  const removeItem = (index) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const getPaidByName = (userId) => {
    const member = trip?.trip_members?.find(
      member => member.profiles?.id === userId
    );
    return member?.profiles?.full_name || 'Unknown';
  };

  const renderPaidByMenu = () => (
    <Menu
      visible={paidByMenuVisible}
      onDismiss={() => setPaidByMenuVisible(false)}
      anchor={
        <Button
          mode="outlined"
          onPress={() => setPaidByMenuVisible(true)}
          style={styles.paidByButton}
        >
          Paid by: {getPaidByName(watch('paidBy'))}
        </Button>
      }
    >
      {trip?.trip_members?.map(member => (
        <Menu.Item
          key={member.profiles?.id}
          onPress={() => {
            setValue('paidBy', member.profiles?.id);
            setPaidByMenuVisible(false);
          }}
          title={member.profiles?.full_name || 'Unknown'}
        />
      ))}
    </Menu>
  );

  const renderItemCard = (item, index) => (
    <Card key={index} style={styles.itemCard}>
      <Card.Content>
        <View style={styles.itemHeader}>
          <Text style={styles.itemTitle}>Item {index + 1}</Text>
          {fields.length > 1 && (
            <IconButton
              icon="delete"
              size={20}
              onPress={() => removeItem(index)}
            />
          )}
        </View>

        <Controller
          control={control}
          name={`items.${index}.name`}
          rules={{ required: 'Item name is required' }}
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              label="Item Name"
              value={value}
              onBlur={onBlur}
              onChangeText={onChange}
              error={!!errors.items?.[index]?.name}
              style={styles.input}
              mode="outlined"
              placeholder="e.g., Dinner, Gas, Hotel"
            />
          )}
        />

        <View style={styles.itemRow}>
          <Controller
            control={control}
            name={`items.${index}.amount`}
            rules={{ 
              required: 'Amount is required',
              pattern: {
                value: /^\d+(\.\d{1,2})?$/,
                message: 'Invalid amount format'
              }
            }}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Amount"
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                error={!!errors.items?.[index]?.amount}
                style={[styles.input, styles.amountInput]}
                mode="outlined"
                keyboardType="decimal-pad"
                placeholder="0.00"
              />
            )}
          />

          <Controller
            control={control}
            name={`items.${index}.quantity`}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Qty"
                value={value?.toString()}
                onBlur={onBlur}
                onChangeText={(text) => onChange(parseInt(text) || 1)}
                style={[styles.input, styles.quantityInput]}
                mode="outlined"
                keyboardType="number-pad"
              />
            )}
          />
        </View>

        <View style={styles.splitOptions}>
          <Controller
            control={control}
            name={`items.${index}.isShared`}
            render={({ field: { onChange, value } }) => (
              <View style={styles.chipContainer}>
                <Chip
                  selected={value}
                  onPress={() => onChange(true)}
                  style={styles.chip}
                >
                  Split Evenly
                </Chip>
                <Chip
                  selected={!value}
                  onPress={() => onChange(false)}
                  style={styles.chip}
                >
                  Custom Split
                </Chip>
              </View>
            )}
          />
        </View>

        {!watch(`items.${index}.isShared`) && (
          <Text style={styles.customSplitNote}>
            Custom splitting will be available in the next step
          </Text>
        )}
      </Card.Content>
    </Card>
  );

  if (!trip) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Basic Info Card */}
          <Card style={styles.card}>
            <Card.Content>
              <Title>Expense Details</Title>
              
              <Controller
                control={control}
                name="title"
                rules={{ required: 'Title is required' }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label="Expense Title"
                    value={value}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    error={!!errors.title}
                    style={styles.input}
                    mode="outlined"
                    placeholder="e.g., Dinner at Restaurant"
                  />
                )}
              />

              <Controller
                control={control}
                name="description"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label="Description (Optional)"
                    value={value}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    style={styles.input}
                    mode="outlined"
                    multiline
                    numberOfLines={2}
                    placeholder="Add notes about this expense..."
                  />
                )}
              />

              <View style={styles.row}>
                <Controller
                  control={control}
                  name="totalAmount"
                  rules={{ 
                    required: 'Total amount is required',
                    pattern: {
                      value: /^\d+(\.\d{1,2})?$/,
                      message: 'Invalid amount format'
                    }
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Total Amount"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.totalAmount}
                      style={[styles.input, styles.totalInput]}
                      mode="outlined"
                      keyboardType="decimal-pad"
                      placeholder="0.00"
                    />
                  )}
                />

                {renderPaidByMenu()}
              </View>
            </Card.Content>
          </Card>

          {/* Items Section */}
          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.sectionHeader}>
                <Title>Items</Title>
                <View style={styles.headerButtons}>
                  <Button
                    mode="outlined"
                    onPress={() => navigation.navigate('ScanReceipt', { tripId })}
                    compact
                    icon="camera"
                    style={styles.scanButton}
                  >
                    Scan
                  </Button>
                  <Button
                    mode="outlined"
                    onPress={addItem}
                    compact
                    icon="plus"
                  >
                    Add Item
                  </Button>
                </View>
              </View>

              {fields.map((field, index) => renderItemCard(field, index))}
            </Card.Content>
          </Card>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              disabled={loading}
              style={styles.cancelButton}
            >
              Cancel
            </Button>
            
            <Button
              mode="contained"
              onPress={handleSubmit(onSubmit)}
              loading={loading}
              disabled={loading}
              style={styles.saveButton}
            >
              Save Expense
            </Button>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  input: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  totalInput: {
    flex: 1,
  },
  paidByButton: {
    flex: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  scanButton: {
    marginRight: 4,
  },
  itemCard: {
    marginBottom: 12,
    backgroundColor: '#fafafa',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemRow: {
    flexDirection: 'row',
    gap: 12,
  },
  amountInput: {
    flex: 2,
  },
  quantityInput: {
    flex: 1,
  },
  splitOptions: {
    marginTop: 12,
  },
  chipContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  chip: {
    flex: 1,
  },
  customSplitNote: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 8,
    fontStyle: 'italic',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
});
