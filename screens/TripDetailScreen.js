import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  FAB,
  Text,
  Avatar,
  Button,
  Chip,
  List,
  Divider,
  IconButton,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { tripService } from '../services/tripService';
import { useAuth } from '../hooks/useAuth';
import TripSummaryCard from '../components/TripSummaryCard';

export default function TripDetailScreen({ route, navigation }) {
  const { tripId } = route.params;
  const { user } = useAuth();
  const [trip, setTrip] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [balanceData, setBalanceData] = useState({ balances: {}, settlements: [] });

  const loadTripDetails = async () => {
    try {
      const [tripResult, balanceResult] = await Promise.all([
        tripService.getTripDetails(tripId),
        tripService.getTripBalances(tripId),
      ]);

      if (tripResult.error) {
        Alert.alert('Error', 'Failed to load trip details');
      } else {
        setTrip(tripResult.data);
      }

      if (balanceResult.error) {
        console.warn('Failed to load balances:', balanceResult.error);
      } else {
        setBalanceData(balanceResult.data || { balances: {}, settlements: [] });
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadTripDetails();
    }, [tripId])
  );

  const onRefresh = () => {
    setRefreshing(true);
    loadTripDetails();
  };

  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((word) => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const isOwner = trip?.trip_members?.some(
    member => member.profiles?.id === user?.id && member.role === 'owner'
  );

  const totalExpenses = trip?.expenses?.reduce(
    (sum, expense) => sum + parseFloat(expense.total_amount),
    0
  ) || 0;

  const handleAddMember = () => {
    // TODO: Implement add member functionality
    Alert.alert('Coming Soon', 'Add member functionality will be implemented');
  };

  const renderMemberItem = (member) => (
    <List.Item
      key={member.profiles?.id}
      title={member.profiles?.full_name || 'Unknown User'}
      description={member.profiles?.email}
      left={() => (
        <Avatar.Text
          size={40}
          label={getInitials(member.profiles?.full_name)}
        />
      )}
      right={() => (
        <View style={styles.memberRight}>
          {member.role === 'owner' && (
            <Chip mode="outlined" compact style={styles.ownerChip}>
              Owner
            </Chip>
          )}
          {balanceData.balances[member.profiles?.id] && (
            <Text style={[
              styles.balanceText,
              balanceData.balances[member.profiles?.id].netBalance > 0 ? styles.positiveBalance : styles.negativeBalance
            ]}>
              {formatCurrency(Math.abs(balanceData.balances[member.profiles?.id].netBalance))}
            </Text>
          )}
        </View>
      )}
    />
  );

  const renderExpenseItem = (expense) => (
    <List.Item
      key={expense.id}
      title={expense.title}
      description={`Paid by ${expense.profiles?.full_name} • ${new Date(expense.created_at).toLocaleDateString()}`}
      left={() => <List.Icon icon="receipt" />}
      right={() => (
        <Text style={styles.expenseAmount}>
          {formatCurrency(expense.total_amount, expense.currency)}
        </Text>
      )}
      onPress={() => navigation.navigate('ExpenseDetail', { expenseId: expense.id })}
    />
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading...</Text>
      </View>
    );
  }

  if (!trip) {
    return (
      <View style={styles.errorContainer}>
        <Text>Trip not found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Trip Header */}
        <Card style={styles.headerCard}>
          <Card.Content>
            <Title>{trip.name}</Title>
            {trip.description && (
              <Paragraph style={styles.description}>
                {trip.description}
              </Paragraph>
            )}
            
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {formatCurrency(totalExpenses)}
                </Text>
                <Text style={styles.statLabel}>Total</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {trip.expenses?.length || 0}
                </Text>
                <Text style={styles.statLabel}>Expenses</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>
                  {trip.trip_members?.length || 0}
                </Text>
                <Text style={styles.statLabel}>Members</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Trip Summary */}
        <TripSummaryCard
          balances={balanceData.balances}
          settlements={balanceData.settlements}
          onSettlementPress={(_settlement) => {
            // TODO: Implement settlement recording
            Alert.alert('Coming Soon', 'Settlement recording will be implemented');
          }}
        />

        {/* Members Section */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Title>Members</Title>
              {isOwner && (
                <IconButton
                  icon="account-plus"
                  onPress={handleAddMember}
                />
              )}
            </View>

            {trip.trip_members?.map(renderMemberItem)}
          </Card.Content>
        </Card>

        {/* Expenses Section */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <Title>Recent Expenses</Title>
            
            {trip.expenses && trip.expenses.length > 0 ? (
              trip.expenses
                .slice(0, 5)
                .map(renderExpenseItem)
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>
                  No expenses yet. Add your first expense!
                </Text>
              </View>
            )}
            
            {trip.expenses && trip.expenses.length > 5 && (
              <Button
                mode="text"
                onPress={() => {
                  // TODO: Navigate to all expenses
                }}
              >
                View All Expenses
              </Button>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => navigation.navigate('AddExpense', { tripId })}
        label="Add Expense"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
    elevation: 2,
  },
  description: {
    opacity: 0.7,
    marginTop: 8,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  sectionCard: {
    margin: 16,
    marginTop: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  memberRight: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  ownerChip: {
    marginBottom: 4,
  },
  balanceText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  positiveBalance: {
    color: '#4CAF50',
  },
  negativeBalance: {
    color: '#F44336',
  },
  expenseAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    alignSelf: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  emptyText: {
    opacity: 0.7,
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});
