import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  Title,
  Avatar,
  Divider,
  List,
} from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import { useAuth } from '../hooks/useAuth';

export default function ProfileScreen() {
  const [loading, setLoading] = useState(false);
  const [profile, setProfile] = useState(null);
  const { user, signOut, updateProfile, getProfile } = useAuth();

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    defaultValues: {
      fullName: '',
      email: '',
    },
  });

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    setLoading(true);
    try {
      const { data, error } = await getProfile();
      if (error) {
        Alert.alert('Error', 'Failed to load profile');
      } else {
        setProfile(data);
        setValue('fullName', data?.full_name || '');
        setValue('email', data?.email || user?.email || '');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data) => {
    setLoading(true);
    try {
      const { error } = await updateProfile({
        full_name: data.fullName,
      });

      if (error) {
        Alert.alert('Error', error.message);
      } else {
        Alert.alert('Success', 'Profile updated successfully!');
        loadProfile();
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            const { error } = await signOut();
            if (error) {
              Alert.alert('Error', error.message);
            }
          },
        },
      ]
    );
  };

  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((word) => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Card style={styles.profileCard}>
          <Card.Content style={styles.profileContent}>
            <Avatar.Text
              size={80}
              label={getInitials(profile?.full_name)}
              style={styles.avatar}
            />
            <Title style={styles.name}>
              {profile?.full_name || 'User'}
            </Title>
            <Text style={styles.email}>
              {profile?.email || user?.email}
            </Text>
          </Card.Content>
        </Card>

        <Card style={styles.formCard}>
          <Card.Content>
            <Title>Edit Profile</Title>
            
            <View style={styles.form}>
              <Controller
                control={control}
                name="fullName"
                rules={{
                  required: 'Full name is required',
                  minLength: {
                    value: 2,
                    message: 'Name must be at least 2 characters',
                  },
                }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    label="Full Name"
                    value={value}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    error={!!errors.fullName}
                    style={styles.input}
                    mode="outlined"
                  />
                )}
              />
              {errors.fullName && (
                <Text style={styles.errorText}>
                  {errors.fullName.message}
                </Text>
              )}

              <TextInput
                label="Email"
                value={profile?.email || user?.email || ''}
                style={styles.input}
                mode="outlined"
                disabled
                right={<TextInput.Icon icon="lock" />}
              />
              <Text style={styles.helperText}>
                Email cannot be changed
              </Text>

              <Button
                mode="contained"
                onPress={handleSubmit(onSubmit)}
                loading={loading}
                disabled={loading}
                style={styles.updateButton}
              >
                Update Profile
              </Button>
            </View>
          </Card.Content>
        </Card>

        <Card style={styles.actionsCard}>
          <Card.Content>
            <Title>Account</Title>
            
            <List.Item
              title="Account Settings"
              description="Manage your account preferences"
              left={(props) => <List.Icon {...props} icon="cog" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                // Navigate to settings
              }}
            />
            
            <Divider />
            
            <List.Item
              title="Privacy Policy"
              description="Read our privacy policy"
              left={(props) => <List.Icon {...props} icon="shield-account" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                // Navigate to privacy policy
              }}
            />
            
            <Divider />
            
            <List.Item
              title="Terms of Service"
              description="Read our terms of service"
              left={(props) => <List.Icon {...props} icon="file-document" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                // Navigate to terms
              }}
            />
            
            <Divider />
            
            <List.Item
              title="Sign Out"
              description="Sign out of your account"
              left={(props) => <List.Icon {...props} icon="logout" />}
              onPress={handleSignOut}
              titleStyle={styles.signOutText}
            />
          </Card.Content>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 16,
  },
  profileCard: {
    marginBottom: 16,
    elevation: 2,
  },
  profileContent: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  avatar: {
    marginBottom: 16,
  },
  name: {
    marginBottom: 4,
  },
  email: {
    opacity: 0.7,
  },
  formCard: {
    marginBottom: 16,
    elevation: 2,
  },
  form: {
    marginTop: 16,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    color: '#B00020',
    fontSize: 12,
    marginBottom: 8,
    marginLeft: 12,
  },
  helperText: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 16,
    marginLeft: 12,
  },
  updateButton: {
    marginTop: 8,
  },
  actionsCard: {
    elevation: 2,
  },
  signOutText: {
    color: '#B00020',
  },
});
