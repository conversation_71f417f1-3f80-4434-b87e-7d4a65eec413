import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Button,
  Card,
  Title,
  Text,
  List,
} from 'react-native-paper';
import * as ImagePicker from 'expo-image-picker';
import { ocrService } from '../services/ocrService';

export default function ScanReceiptScreenSimple({ route, navigation }) {
  const { tripId } = route.params;
  const [processing, setProcessing] = useState(false);
  const [ocrResult, setOcrResult] = useState(null);

  const pickImageFromGallery = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant access to your photo library');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [3, 4],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        processImage(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image from gallery');
    }
  };

  const processImage = async (imageUri) => {
    setProcessing(true);
    setOcrResult(null);

    try {
      const result = await ocrService.processReceiptImage(imageUri);
      
      if (result.success) {
        setOcrResult(result);
        
        if (result.items.length === 0) {
          Alert.alert(
            'No Items Found',
            'Could not detect any items in the receipt. You can add them manually.',
            [
              {
                text: 'Try Again',
                onPress: () => setOcrResult(null),
              },
              {
                text: 'Add Manually',
                onPress: () => navigation.navigate('AddExpense', { tripId }),
              },
            ]
          );
        }
      } else {
        Alert.alert('Processing Failed', result.error || 'Failed to process receipt');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred while processing the receipt');
    } finally {
      setProcessing(false);
    }
  };

  const useScannedData = () => {
    if (ocrResult && ocrResult.items.length > 0) {
      navigation.navigate('AddExpense', {
        tripId,
        scannedData: {
          items: ocrService.validateAndCleanItems(ocrResult.items),
          totalAmount: ocrResult.totals.total || 0,
          title: 'Scanned Receipt',
        },
      });
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount || 0);
  };

  return (
    <View style={styles.container}>
      <Card style={styles.instructionCard}>
        <Card.Content>
          <Title>Receipt Scanner</Title>
          <Text style={styles.description}>
            Select a photo of your receipt from your gallery to automatically extract items and amounts.
          </Text>
          
          <Button
            mode="contained"
            onPress={pickImageFromGallery}
            icon="image"
            style={styles.selectButton}
            loading={processing}
            disabled={processing}
          >
            {processing ? 'Processing...' : 'Select Receipt Photo'}
          </Button>
        </Card.Content>
      </Card>

      {/* OCR Results */}
      {ocrResult && !processing && (
        <Card style={styles.resultsCard}>
          <Card.Content>
            <Title>Scanned Items</Title>

            {ocrResult.items.length > 0 ? (
              <>
                {ocrResult.items.map((item, index) => (
                  <List.Item
                    key={index}
                    title={item.name}
                    description={`Quantity: ${item.quantity}`}
                    right={() => (
                      <Text style={styles.itemAmount}>
                        {formatCurrency(item.amount)}
                      </Text>
                    )}
                  />
                ))}
                
                {ocrResult.totals.total > 0 && (
                  <View style={styles.totalSection}>
                    <Text style={styles.totalLabel}>Total:</Text>
                    <Text style={styles.totalAmount}>
                      {formatCurrency(ocrResult.totals.total)}
                    </Text>
                  </View>
                )}
              </>
            ) : (
              <Text style={styles.noItemsText}>
                No items were detected in the receipt.
              </Text>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        {ocrResult && ocrResult.items.length > 0 && (
          <Button
            mode="contained"
            onPress={useScannedData}
            style={styles.actionButton}
            icon="check"
          >
            Use Scanned Data
          </Button>
        )}
        
        <Button
          mode="outlined"
          onPress={() => navigation.navigate('AddExpense', { tripId })}
          style={styles.actionButton}
        >
          Add Manually Instead
        </Button>
        
        <Button
          mode="text"
          onPress={() => navigation.goBack()}
          style={styles.actionButton}
        >
          Cancel
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  instructionCard: {
    marginBottom: 16,
    elevation: 4,
  },
  description: {
    marginVertical: 16,
    opacity: 0.8,
    lineHeight: 20,
  },
  selectButton: {
    marginTop: 8,
  },
  resultsCard: {
    marginBottom: 16,
    elevation: 4,
  },
  itemAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    alignSelf: 'center',
  },
  totalSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  noItemsText: {
    textAlign: 'center',
    opacity: 0.7,
    paddingVertical: 20,
  },
  actionButtons: {
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
});
