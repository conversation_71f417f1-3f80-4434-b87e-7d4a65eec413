import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Text,
  List,
  Chip,
  Avatar,
  Button,
  Divider,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { expenseService } from '../services/expenseService';
import { useAuth } from '../hooks/useAuth';

export default function ExpenseDetailScreen({ route, navigation }) {
  const { expenseId } = route.params;
  const { user } = useAuth();
  const [expense, setExpense] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadExpenseDetails = async () => {
    try {
      const { data, error } = await expenseService.getExpenseDetails(expenseId);
      
      if (error) {
        Alert.alert('Error', 'Failed to load expense details');
      } else {
        setExpense(data);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadExpenseDetails();
    }, [expenseId])
  );

  const onRefresh = () => {
    setRefreshing(true);
    loadExpenseDetails();
  };

  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((word) => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const canEdit = expense?.paid_by === user?.id;

  const handleEdit = () => {
    // TODO: Navigate to edit expense screen
    Alert.alert('Coming Soon', 'Edit expense functionality will be implemented');
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Expense',
      'Are you sure you want to delete this expense? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await expenseService.deleteExpense(expenseId);
              if (error) {
                Alert.alert('Error', error.message);
              } else {
                navigation.goBack();
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to delete expense');
            }
          },
        },
      ]
    );
  };

  const renderExpenseItem = (item) => (
    <Card key={item.id} style={styles.itemCard}>
      <Card.Content>
        <View style={styles.itemHeader}>
          <View style={styles.itemInfo}>
            <Text style={styles.itemName}>{item.name}</Text>
            <Text style={styles.itemAmount}>
              {formatCurrency(item.amount, expense.currency)}
            </Text>
          </View>
          
          {item.quantity > 1 && (
            <Chip mode="outlined" compact>
              Qty: {item.quantity}
            </Chip>
          )}
        </View>

        <View style={styles.splitInfo}>
          <Text style={styles.splitLabel}>
            {item.is_shared ? 'Split evenly among:' : 'Assigned to:'}
          </Text>
          
          <View style={styles.splitMembers}>
            {item.expense_splits?.map((split) => (
              <View key={split.id} style={styles.splitMember}>
                <Avatar.Text
                  size={32}
                  label={getInitials(split.profiles?.full_name)}
                />
                <View style={styles.splitMemberInfo}>
                  <Text style={styles.splitMemberName}>
                    {split.profiles?.full_name || 'Unknown'}
                  </Text>
                  <Text style={styles.splitMemberAmount}>
                    {formatCurrency(split.amount, expense.currency)}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading...</Text>
      </View>
    );
  }

  if (!expense) {
    return (
      <View style={styles.errorContainer}>
        <Text>Expense not found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Expense Header */}
        <Card style={styles.headerCard}>
          <Card.Content>
            <Title>{expense.title}</Title>
            
            {expense.description && (
              <Paragraph style={styles.description}>
                {expense.description}
              </Paragraph>
            )}

            <View style={styles.expenseInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Total Amount:</Text>
                <Text style={styles.totalAmount}>
                  {formatCurrency(expense.total_amount, expense.currency)}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Paid by:</Text>
                <View style={styles.paidByInfo}>
                  <Avatar.Text
                    size={24}
                    label={getInitials(expense.profiles?.full_name)}
                  />
                  <Text style={styles.paidByName}>
                    {expense.profiles?.full_name || 'Unknown'}
                  </Text>
                </View>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Date:</Text>
                <Text style={styles.infoValue}>
                  {new Date(expense.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Trip:</Text>
                <Text style={styles.infoValue}>
                  {expense.trips?.name || 'Unknown Trip'}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Items Section */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <Title>Items ({expense.expense_items?.length || 0})</Title>
            
            {expense.expense_items && expense.expense_items.length > 0 ? (
              expense.expense_items.map(renderExpenseItem)
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>
                  No items found for this expense.
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Actions */}
        {canEdit && (
          <Card style={styles.actionsCard}>
            <Card.Content>
              <Title>Actions</Title>
              
              <View style={styles.actionButtons}>
                <Button
                  mode="outlined"
                  onPress={handleEdit}
                  style={styles.actionButton}
                  icon="pencil"
                >
                  Edit Expense
                </Button>
                
                <Button
                  mode="outlined"
                  onPress={handleDelete}
                  style={[styles.actionButton, styles.deleteButton]}
                  icon="delete"
                  textColor="#B00020"
                >
                  Delete Expense
                </Button>
              </View>
            </Card.Content>
          </Card>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
    elevation: 2,
  },
  description: {
    opacity: 0.7,
    marginTop: 8,
    marginBottom: 16,
  },
  expenseInfo: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  paidByInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  paidByName: {
    fontSize: 14,
    fontWeight: '500',
  },
  sectionCard: {
    margin: 16,
    marginTop: 8,
    elevation: 2,
  },
  itemCard: {
    marginTop: 12,
    backgroundColor: '#fafafa',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemAmount: {
    fontSize: 14,
    color: '#2E7D32',
    fontWeight: 'bold',
  },
  splitInfo: {
    marginTop: 8,
  },
  splitLabel: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 8,
  },
  splitMembers: {
    gap: 8,
  },
  splitMember: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  splitMemberInfo: {
    flex: 1,
  },
  splitMemberName: {
    fontSize: 14,
    fontWeight: '500',
  },
  splitMemberAmount: {
    fontSize: 12,
    opacity: 0.7,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  emptyText: {
    opacity: 0.7,
    textAlign: 'center',
  },
  actionsCard: {
    margin: 16,
    marginTop: 8,
    elevation: 2,
  },
  actionButtons: {
    gap: 12,
    marginTop: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
  deleteButton: {
    borderColor: '#B00020',
  },
});
