import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  TextInput,
  Button,
  Card,
  Title,
  HelperText,
} from 'react-native-paper';
import { useForm, Controller } from 'react-hook-form';
import { tripService } from '../services/tripService';

export default function CreateTripScreen({ navigation }) {
  const [loading, setLoading] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: '',
      description: '',
    },
  });

  const onSubmit = async (data) => {
    setLoading(true);
    try {
      // Debug: Check if user is authenticated
      console.log('Creating trip with data:', data);

      const { data: trip, error } = await tripService.createTrip(data);

      if (error) {
        console.error('Trip creation error:', error);
        Alert.alert('Error', error.message || 'Failed to create trip');
      } else {
        Alert.alert(
          'Success',
          'Trip created successfully!',
          [
            {
              text: 'OK',
              onPress: () => {
                navigation.goBack();
                // Navigate to the new trip
                navigation.navigate('TripDetail', { tripId: trip.id });
              },
            },
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.title}>Create New Trip</Title>
              
              <View style={styles.form}>
                <Controller
                  control={control}
                  name="name"
                  rules={{
                    required: 'Trip name is required',
                    minLength: {
                      value: 2,
                      message: 'Trip name must be at least 2 characters',
                    },
                    maxLength: {
                      value: 50,
                      message: 'Trip name must be less than 50 characters',
                    },
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Trip Name *"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.name}
                      style={styles.input}
                      mode="outlined"
                      placeholder="e.g., Weekend in Paris"
                    />
                  )}
                />
                <HelperText type="error" visible={!!errors.name}>
                  {errors.name?.message}
                </HelperText>

                <Controller
                  control={control}
                  name="description"
                  rules={{
                    maxLength: {
                      value: 200,
                      message: 'Description must be less than 200 characters',
                    },
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      label="Description (Optional)"
                      value={value}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      error={!!errors.description}
                      style={styles.input}
                      mode="outlined"
                      multiline
                      numberOfLines={3}
                      placeholder="Add details about your trip..."
                    />
                  )}
                />
                <HelperText type="error" visible={!!errors.description}>
                  {errors.description?.message}
                </HelperText>

                <View style={styles.buttonContainer}>
                  <Button
                    mode="outlined"
                    onPress={() => navigation.goBack()}
                    disabled={loading}
                    style={styles.cancelButton}
                  >
                    Cancel
                  </Button>
                  
                  <Button
                    mode="contained"
                    onPress={handleSubmit(onSubmit)}
                    loading={loading}
                    disabled={loading}
                    style={styles.createButton}
                  >
                    Create Trip
                  </Button>
                </View>
              </View>
            </Card.Content>
          </Card>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  card: {
    elevation: 2,
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
  },
  form: {
    gap: 8,
  },
  input: {
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
  },
  createButton: {
    flex: 1,
  },
});
