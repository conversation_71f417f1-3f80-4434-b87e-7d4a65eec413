import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Image,
  Dimensions,
} from 'react-native';
import {
  Button,
  Card,
  Title,
  Text,
  ActivityIndicator,
  List,
  IconButton,
  Chip,
} from 'react-native-paper';
import { Camera } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { ocrService } from '../services/ocrService';

const { width: screenWidth } = Dimensions.get('window');

export default function ScanReceiptScreen({ route, navigation }) {
  const { tripId } = route.params;
  const [hasPermission, setHasPermission] = useState(null);
  const [cameraReady, setCameraReady] = useState(false);
  const [capturedImage, setCapturedImage] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [ocrResult, setOcrResult] = useState(null);
  const [showCamera, setShowCamera] = useState(true);
  const cameraRef = useRef(null);

  React.useEffect(() => {
    (async () => {
      try {
        const { status } = await Camera.requestCameraPermissionsAsync();
        setHasPermission(status === 'granted');
      } catch (error) {
        console.error('Camera permission error:', error);
        setHasPermission(false);
      }
    })();
  }, []);

  const takePicture = async () => {
    if (cameraRef.current && cameraReady) {
      try {
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.8,
          base64: false,
        });
        
        setCapturedImage(photo.uri);
        setShowCamera(false);
        processImage(photo.uri);
      } catch (error) {
        Alert.alert('Error', 'Failed to take picture');
      }
    }
  };

  const pickImageFromGallery = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant access to your photo library');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [3, 4],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setCapturedImage(result.assets[0].uri);
        setShowCamera(false);
        processImage(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image from gallery');
    }
  };

  const processImage = async (imageUri) => {
    setProcessing(true);
    setOcrResult(null);

    try {
      const result = await ocrService.processReceiptImage(imageUri);
      
      if (result.success) {
        setOcrResult(result);
        
        if (result.items.length === 0) {
          Alert.alert(
            'No Items Found',
            'Could not detect any items in the receipt. You can add them manually.',
            [
              {
                text: 'Try Again',
                onPress: () => {
                  setCapturedImage(null);
                  setShowCamera(true);
                  setOcrResult(null);
                },
              },
              {
                text: 'Add Manually',
                onPress: () => navigation.navigate('AddExpense', { tripId }),
              },
            ]
          );
        }
      } else {
        Alert.alert('Processing Failed', result.error || 'Failed to process receipt');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred while processing the receipt');
    } finally {
      setProcessing(false);
    }
  };

  const retakePhoto = () => {
    setCapturedImage(null);
    setOcrResult(null);
    setShowCamera(true);
  };

  const useScannedData = () => {
    if (ocrResult && ocrResult.items.length > 0) {
      // Navigate to AddExpense with pre-filled data
      navigation.navigate('AddExpense', {
        tripId,
        scannedData: {
          items: ocrService.validateAndCleanItems(ocrResult.items),
          totalAmount: ocrResult.totals.total || 0,
          title: 'Scanned Receipt',
        },
      });
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount || 0);
  };

  if (hasPermission === null) {
    return (
      <View style={styles.centerContainer}>
        <Text>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.noPermissionText}>
          Camera permission is required to scan receipts
        </Text>
        <Button
          mode="contained"
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          Go Back
        </Button>
      </View>
    );
  }

  if (showCamera) {
    return (
      <View style={styles.container}>
        <Camera
          ref={cameraRef}
          style={styles.camera}
          facing="back"
          onCameraReady={() => setCameraReady(true)}
        >
          <View style={styles.cameraOverlay}>
            <View style={styles.topOverlay}>
              <Text style={styles.instructionText}>
                Position the receipt within the frame
              </Text>
            </View>
            
            <View style={styles.frameContainer}>
              <View style={styles.frame} />
            </View>
            
            <View style={styles.bottomOverlay}>
              <View style={styles.cameraControls}>
                <Button
                  mode="outlined"
                  onPress={pickImageFromGallery}
                  icon="image"
                  textColor="white"
                  style={styles.galleryButton}
                >
                  Gallery
                </Button>
                
                <IconButton
                  icon="camera"
                  size={60}
                  iconColor="white"
                  style={styles.captureButton}
                  onPress={takePicture}
                  disabled={!cameraReady}
                />
                
                <Button
                  mode="outlined"
                  onPress={() => navigation.goBack()}
                  icon="close"
                  textColor="white"
                  style={styles.cancelButton}
                >
                  Cancel
                </Button>
              </View>
            </View>
          </View>
        </Camera>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Captured Image */}
      {capturedImage && (
        <Card style={styles.imageCard}>
          <Card.Content>
            <Image source={{ uri: capturedImage }} style={styles.capturedImage} />
          </Card.Content>
        </Card>
      )}

      {/* Processing Indicator */}
      {processing && (
        <Card style={styles.processingCard}>
          <Card.Content style={styles.processingContent}>
            <ActivityIndicator size="large" />
            <Text style={styles.processingText}>
              Processing receipt...
            </Text>
          </Card.Content>
        </Card>
      )}

      {/* OCR Results */}
      {ocrResult && !processing && (
        <Card style={styles.resultsCard}>
          <Card.Content>
            <View style={styles.resultsHeader}>
              <Title>Scanned Items</Title>
              <Chip mode="outlined">
                {Math.round(ocrResult.confidence * 100)}% confidence
              </Chip>
            </View>

            {ocrResult.items.length > 0 ? (
              <>
                {ocrResult.items.map((item, index) => (
                  <List.Item
                    key={index}
                    title={item.name}
                    description={`Quantity: ${item.quantity}`}
                    right={() => (
                      <Text style={styles.itemAmount}>
                        {formatCurrency(item.amount)}
                      </Text>
                    )}
                  />
                ))}
                
                {ocrResult.totals.total > 0 && (
                  <View style={styles.totalSection}>
                    <Text style={styles.totalLabel}>Total:</Text>
                    <Text style={styles.totalAmount}>
                      {formatCurrency(ocrResult.totals.total)}
                    </Text>
                  </View>
                )}
              </>
            ) : (
              <Text style={styles.noItemsText}>
                No items were detected in the receipt.
              </Text>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={retakePhoto}
          style={styles.actionButton}
          icon="camera-retake"
        >
          Retake
        </Button>
        
        {ocrResult && ocrResult.items.length > 0 && (
          <Button
            mode="contained"
            onPress={useScannedData}
            style={styles.actionButton}
            icon="check"
          >
            Use Scanned Data
          </Button>
        )}
        
        <Button
          mode="text"
          onPress={() => navigation.navigate('AddExpense', { tripId })}
          style={styles.actionButton}
        >
          Add Manually
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  topOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  frameContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  frame: {
    width: screenWidth * 0.8,
    height: 180,
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  bottomOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  cameraControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingBottom: 40,
    paddingHorizontal: 20,
  },
  galleryButton: {
    borderColor: 'white',
  },
  captureButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  cancelButton: {
    borderColor: 'white',
  },
  imageCard: {
    margin: 16,
    elevation: 4,
  },
  capturedImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  processingCard: {
    margin: 16,
    elevation: 4,
  },
  processingContent: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  processingText: {
    marginTop: 12,
    fontSize: 16,
  },
  resultsCard: {
    margin: 16,
    elevation: 4,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  itemAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  totalSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E7D32',
  },
  noItemsText: {
    textAlign: 'center',
    opacity: 0.7,
    paddingVertical: 20,
  },
  actionButtons: {
    padding: 16,
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
  noPermissionText: {
    textAlign: 'center',
    marginBottom: 20,
    fontSize: 16,
  },
  backButton: {
    marginTop: 20,
  },
});
