import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  FAB,
  Text,
  Chip,
  Avatar,
} from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';
import { tripService } from '../services/tripService';
import EmptyState from '../components/EmptyState';

export default function TripsScreen({ navigation }) {
  const [trips, setTrips] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadTrips = async () => {
    try {
      const { data, error } = await tripService.getUserTrips();
      if (error) {
        Alert.alert('Error', 'Failed to load trips');
      } else {
        setTrips(data || []);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      loadTrips();
    }, [])
  );

  const onRefresh = () => {
    setRefreshing(true);
    loadTrips();
  };

  const calculateTripTotal = (expenses) => {
    if (!expenses || expenses.length === 0) return 0;
    return expenses.reduce((total, expense) => total + parseFloat(expense.total_amount), 0);
  };

  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const renderTripCard = ({ item: trip }) => {
    const totalAmount = calculateTripTotal(trip.expenses);
    const memberCount = trip.trip_members?.length || 0;
    const userRole = trip.trip_members?.find(member => member.profiles)?.role;

    return (
      <Card
        style={styles.tripCard}
        onPress={() => navigation.navigate('TripDetail', { tripId: trip.id })}
      >
        <Card.Content>
          <View style={styles.tripHeader}>
            <View style={styles.tripInfo}>
              <Title numberOfLines={1}>{trip.name}</Title>
              {trip.description && (
                <Paragraph numberOfLines={2} style={styles.description}>
                  {trip.description}
                </Paragraph>
              )}
            </View>
            {userRole === 'owner' && (
              <Chip mode="outlined" compact>
                Owner
              </Chip>
            )}
          </View>

          <View style={styles.tripStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {formatCurrency(totalAmount)}
              </Text>
              <Text style={styles.statLabel}>Total Expenses</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{trip.expenses?.length || 0}</Text>
              <Text style={styles.statLabel}>Expenses</Text>
            </View>
            
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{memberCount}</Text>
              <Text style={styles.statLabel}>Members</Text>
            </View>
          </View>

          <View style={styles.membersPreview}>
            <View style={styles.avatarGroup}>
              {trip.trip_members?.slice(0, 3).map((member, index) => (
                <Avatar.Text
                  key={member.profiles?.id || index}
                  size={32}
                  label={getInitials(member.profiles?.full_name)}
                  style={[styles.memberAvatar, { marginLeft: index > 0 ? -8 : 0 }]}
                />
              ))}
              {memberCount > 3 && (
                <Avatar.Text
                  size={32}
                  label={`+${memberCount - 3}`}
                  style={[styles.memberAvatar, { marginLeft: -8 }]}
                />
              )}
            </View>
            
            <Text style={styles.lastActivity}>
              Created {new Date(trip.created_at).toLocaleDateString()}
            </Text>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const getInitials = (name) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((word) => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const renderEmptyState = () => (
    <EmptyState
      icon="map-marker-plus"
      title="No Trips Yet"
      description="Create your first trip to start tracking expenses with friends!"
      actionText="Create Trip"
      onActionPress={() => navigation.navigate('CreateTrip')}
    />
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={trips}
        renderItem={renderTripCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={trips.length === 0 ? styles.emptyContainer : styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />
      
      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => navigation.navigate('CreateTrip')}
        label="New Trip"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  listContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  tripCard: {
    marginBottom: 16,
    elevation: 2,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  tripInfo: {
    flex: 1,
    marginRight: 8,
  },
  description: {
    opacity: 0.7,
    marginTop: 4,
  },
  tripStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e0e0e0',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  membersPreview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  avatarGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  memberAvatar: {
    borderWidth: 2,
    borderColor: 'white',
  },
  lastActivity: {
    fontSize: 12,
    opacity: 0.7,
  },
  emptyState: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    textAlign: 'center',
    opacity: 0.7,
    marginTop: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});
