-- Debug authentication and RLS issues
-- Run this to check if auth is working properly

-- Check if auth.uid() is working
SELECT auth.uid() as current_user_id;

-- Check if there are any users in auth.users
SELECT id, email, created_at FROM auth.users LIMIT 5;

-- Check if there are profiles
SELECT id, email, full_name FROM public.profiles LIMIT 5;

-- Temporarily disable R<PERSON> on trips table for testing
-- (ONLY for debugging - re-enable after testing!)
ALTER TABLE public.trips DISABLE ROW LEVEL SECURITY;

-- After testing trip creation, re-enable RLS:
-- ALTER TABLE public.trips ENABLE ROW LEVEL SECURITY;
