-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- <PERSON><PERSON> custom types
CREATE TYPE expense_status AS ENUM ('pending', 'settled');
CREATE TYPE user_role AS ENUM ('owner', 'member');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trips table
CREATE TABLE public.trips (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  created_by UUID REFERENCES public.profiles(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);

-- Trip members junction table
CREATE TABLE public.trip_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  role user_role DEFAULT 'member',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(trip_id, user_id)
);

-- Expenses table
CREATE TABLE public.expenses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  total_amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  paid_by UUID REFERENCES public.profiles(id) NOT NULL,
  receipt_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status expense_status DEFAULT 'pending'
);

-- Expense items table (for itemized expenses)
CREATE TABLE public.expense_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  expense_id UUID REFERENCES public.expenses(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  quantity INTEGER DEFAULT 1,
  is_shared BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Expense splits table (who owes what for each expense item)
CREATE TABLE public.expense_splits (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  expense_item_id UUID REFERENCES public.expense_items(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(expense_item_id, user_id)
);

-- Settlements table (track payments between users)
CREATE TABLE public.settlements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE,
  from_user UUID REFERENCES public.profiles(id) NOT NULL,
  to_user UUID REFERENCES public.profiles(id) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  settled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notes TEXT
);

-- Create indexes for better performance
CREATE INDEX idx_trips_created_by ON public.trips(created_by);
CREATE INDEX idx_trip_members_trip_id ON public.trip_members(trip_id);
CREATE INDEX idx_trip_members_user_id ON public.trip_members(user_id);
CREATE INDEX idx_expenses_trip_id ON public.expenses(trip_id);
CREATE INDEX idx_expenses_paid_by ON public.expenses(paid_by);
CREATE INDEX idx_expense_items_expense_id ON public.expense_items(expense_id);
CREATE INDEX idx_expense_splits_expense_item_id ON public.expense_splits(expense_item_id);
CREATE INDEX idx_expense_splits_user_id ON public.expense_splits(user_id);
CREATE INDEX idx_settlements_trip_id ON public.settlements(trip_id);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trip_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expense_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expense_splits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settlements ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Profiles: Users can read all profiles but only update their own
CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- Trips: Users can see trips they're members of
CREATE POLICY "Users can view trips they're members of" ON public.trips
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members WHERE trip_id = trips.id
    )
  );

CREATE POLICY "Users can create trips" ON public.trips
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Trip owners can update trips" ON public.trips
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members 
      WHERE trip_id = trips.id AND role = 'owner'
    )
  );

-- Trip members: Users can see members of trips they're in
CREATE POLICY "Users can view trip members of their trips" ON public.trip_members
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members tm2 WHERE tm2.trip_id = trip_members.trip_id
    )
  );

CREATE POLICY "Trip owners can manage members" ON public.trip_members
  FOR ALL USING (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members 
      WHERE trip_id = trip_members.trip_id AND role = 'owner'
    )
  );

-- Expenses: Users can see expenses from trips they're members of
CREATE POLICY "Users can view expenses from their trips" ON public.expenses
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members WHERE trip_id = expenses.trip_id
    )
  );

CREATE POLICY "Trip members can create expenses" ON public.expenses
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members WHERE trip_id = expenses.trip_id
    )
  );

CREATE POLICY "Expense creators can update their expenses" ON public.expenses
  FOR UPDATE USING (auth.uid() = paid_by);

-- Expense items: Users can see items from expenses in their trips
CREATE POLICY "Users can view expense items from their trips" ON public.expense_items
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members tm
      JOIN public.expenses e ON tm.trip_id = e.trip_id
      WHERE e.id = expense_items.expense_id
    )
  );

CREATE POLICY "Expense creators can manage expense items" ON public.expense_items
  FOR ALL USING (
    auth.uid() IN (
      SELECT paid_by FROM public.expenses WHERE id = expense_items.expense_id
    )
  );

-- Expense splits: Users can see splits from their trips
CREATE POLICY "Users can view expense splits from their trips" ON public.expense_splits
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members tm
      JOIN public.expenses e ON tm.trip_id = e.trip_id
      JOIN public.expense_items ei ON e.id = ei.expense_id
      WHERE ei.id = expense_splits.expense_item_id
    )
  );

CREATE POLICY "Expense creators can manage splits" ON public.expense_splits
  FOR ALL USING (
    auth.uid() IN (
      SELECT e.paid_by FROM public.expenses e
      JOIN public.expense_items ei ON e.id = ei.expense_id
      WHERE ei.id = expense_splits.expense_item_id
    )
  );

-- Settlements: Users can see settlements from their trips
CREATE POLICY "Users can view settlements from their trips" ON public.settlements
  FOR SELECT USING (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members WHERE trip_id = settlements.trip_id
    )
  );

CREATE POLICY "Trip members can create settlements" ON public.settlements
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT user_id FROM public.trip_members WHERE trip_id = settlements.trip_id
    )
  );

-- Functions for automatic profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.trips
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.expenses
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();
