-- Fix for trip creation RLS policy issue
-- Run this in your Supabase SQL Editor

-- Drop existing trip policies
DROP POLICY IF EXISTS "Users can view trips they're members of" ON public.trips;
DROP POLICY IF EXISTS "Users can create trips" ON public.trips;
DROP POLICY IF EXISTS "Trip owners can update trips" ON public.trips;

-- Create simpler, working policies for trips

-- Allow users to create trips (they must be the creator)
CREATE POLICY "Users can create trips" ON public.trips
  FOR INSERT WITH CHECK (
    auth.uid() = created_by
  );

-- Allow users to view trips they created or are members of
CREATE POLICY "Users can view their trips" ON public.trips
  FOR SELECT USING (
    auth.uid() = created_by 
    OR auth.uid() IN (
      SELECT user_id FROM public.trip_members WHERE trip_id = trips.id
    )
  );

-- Allow trip creators to update their trips
CREATE POLICY "Trip creators can update trips" ON public.trips
  FOR UPDATE USING (
    auth.uid() = created_by
  );

-- Allow trip creators to delete their trips
CREATE POLICY "Trip creators can delete trips" ON public.trips
  FOR DELETE USING (
    auth.uid() = created_by
  );

-- Also fix the trip_members policies to work with the new trip policies
DROP POLICY IF EXISTS "Users can view trip members" ON public.trip_members;
DROP POLICY IF EXISTS "Trip creators can add members" ON public.trip_members;
DROP POLICY IF EXISTS "Users can leave trips" ON public.trip_members;

-- Allow users to view trip members if they created the trip
CREATE POLICY "Users can view trip members" ON public.trip_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.trips
      WHERE trips.id = trip_members.trip_id
      AND trips.created_by = auth.uid()
    )
    OR trip_members.user_id = auth.uid()
  );

-- Allow trip creators to add members
CREATE POLICY "Trip creators can add members" ON public.trip_members
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.trips 
      WHERE trips.id = trip_members.trip_id 
      AND trips.created_by = auth.uid()
    )
  );

-- Allow users to leave trips (remove themselves)
CREATE POLICY "Users can leave trips" ON public.trip_members
  FOR DELETE USING (
    trip_members.user_id = auth.uid()
  );

-- Allow trip creators to remove members
CREATE POLICY "Trip creators can remove members" ON public.trip_members
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.trips 
      WHERE trips.id = trip_members.trip_id 
      AND trips.created_by = auth.uid()
    )
  );
