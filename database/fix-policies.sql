-- Fix for infinite recursion in trip_members policies
-- Run this in your Supabase SQL Editor to fix the policy issues

-- Drop the problematic policies
DROP POLICY IF EXISTS "Users can view trip members of their trips" ON public.trip_members;
DROP POLICY IF EXISTS "Trip owners can manage members" ON public.trip_members;

-- Create fixed policies that don't cause recursion

-- Allow users to view trip members if they are members of the trip
CREATE POLICY "Users can view trip members of their trips" ON public.trip_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.trip_members tm2 
      WHERE tm2.trip_id = trip_members.trip_id 
      AND tm2.user_id = auth.uid()
    )
  );

-- Allow trip creators to insert members (when creating a trip)
CREATE POLICY "Trip creators can add initial members" ON public.trip_members
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT created_by FROM public.trips 
      WHERE id = trip_members.trip_id
    )
  );

-- Allow owners to insert new members
CREATE POLICY "Trip owners can add members" ON public.trip_members
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.trip_members existing
      WHERE existing.trip_id = trip_members.trip_id 
      AND existing.user_id = auth.uid()
      AND existing.role = 'owner'
    )
  );

-- Allow owners to delete members (except themselves)
CREATE POLICY "Trip owners can remove members" ON public.trip_members
  FOR DELETE USING (
    trip_members.user_id != auth.uid() -- Can't remove themselves
    AND EXISTS (
      SELECT 1 FROM public.trip_members existing
      WHERE existing.trip_id = trip_members.trip_id 
      AND existing.user_id = auth.uid()
      AND existing.role = 'owner'
    )
  );

-- Allow users to remove themselves from trips
CREATE POLICY "Users can leave trips" ON public.trip_members
  FOR DELETE USING (
    trip_members.user_id = auth.uid()
  );
