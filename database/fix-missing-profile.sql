-- Fix missing profile issue
-- This will create profiles for users who don't have them

-- First, let's see what users exist without profiles
SELECT 
  u.id, 
  u.email, 
  u.created_at,
  p.id as profile_exists
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;

-- Create profiles for users who don't have them
INSERT INTO public.profiles (id, email, full_name)
SELECT 
  u.id, 
  u.email, 
  COALESCE(u.raw_user_meta_data->>'full_name', 'User') as full_name
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;

-- Verify the profiles were created
SELECT id, email, full_name, created_at FROM public.profiles;
