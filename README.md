# Expense Tracker MVP

A React Native app for tracking shared expenses during trips with friends or groups.

## Features

- **Trip Management**: Create trips and invite friends
- **Manual Expense Entry**: Add expenses with itemized details
- **Receipt Scanning**: Scan receipts using camera and OCR
- **Smart Splitting**: Split expenses evenly or assign specific items to individuals
- **Balance Tracking**: See who owes what to whom
- **Settlement Tracking**: Record payments between group members

## Tech Stack

- **Frontend**: React Native with Expo
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Navigation**: React Navigation
- **UI Components**: React Native Paper
- **Forms**: React Hook Form
- **Camera/OCR**: Expo Camera + Image Picker

## Setup Instructions

### 1. Supabase Setup

1. **Create a Supabase Project**
   - Go to [supabase.com](https://supabase.com) and create a new account
   - Click "New Project" and fill in the details
   - Wait for the project to be set up (this may take a few minutes)

2. **Get Your Project Credentials**
   - Go to Settings > API in your Supabase dashboard
   - Copy your Project URL and anon/public key
   - You'll need these for the next step

3. **Set Up the Database**
   - Go to the SQL Editor in your Supabase dashboard
   - Copy the entire contents of `database/schema.sql`
   - Paste it into the SQL editor and run it
   - This will create all the necessary tables, functions, and security policies

4. **Configure the App**
   - Open `lib/supabase.js`
   - Replace `YOUR_SUPABASE_URL` with your actual project URL
   - Replace `YOUR_SUPABASE_ANON_KEY` with your actual anon key

### 2. Local Development

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start the Development Server**
   ```bash
   npm start
   ```
   This will start the Expo development server and show a QR code.

3. **Run on Device/Simulator**
   - **iOS**: `npm run ios` (requires Xcode and iOS Simulator)
   - **Android**: `npm run android` (requires Android Studio and emulator)
   - **Physical Device**: Install Expo Go app and scan the QR code

### 3. Testing the App

1. **Run Tests**
   ```bash
   npm test
   ```

2. **Test the Core Features**
   - Sign up with a new account
   - Create your first trip
   - Add some expenses manually
   - Try the receipt scanning feature (uses mock data)
   - Invite friends by email (they need to sign up first)
   - Check the trip summary and balance calculations

### 4. Environment Variables (Optional)

For production deployment, create a `.env` file:

```
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

Note: For this MVP, credentials are directly in `lib/supabase.js` for simplicity.

## Database Schema

The app uses the following main tables:

- **profiles**: User profiles (extends Supabase auth)
- **trips**: Trip information
- **trip_members**: Users associated with each trip
- **expenses**: Individual expenses
- **expense_items**: Itemized breakdown of expenses
- **expense_splits**: How each item is split among users
- **settlements**: Payments between users

## App Structure

```
├── components/          # Reusable UI components
├── screens/            # Screen components
├── navigation/         # Navigation configuration
├── lib/               # Utilities and configurations
├── hooks/             # Custom React hooks
├── services/          # API and business logic
└── database/          # Database schema and migrations
```

## Key Features Implementation

### Receipt Scanning
- Uses Expo Camera for taking photos
- Integrates with OCR service to extract text
- Parses receipt data to create expense items

### Expense Splitting
- Supports even splitting across all members
- Allows individual assignment of items
- Calculates complex splits automatically

### Balance Calculation
- Tracks who paid what
- Calculates net balances between users
- Suggests optimal settlement strategy

## Current Limitations & Future Enhancements

### Current MVP Limitations
- Receipt OCR uses mock data (integrate with real OCR service like Google Vision API)
- No real-time updates (implement with Supabase real-time subscriptions)
- Basic UI styling (can be enhanced with custom themes)
- No push notifications for new expenses or settlements
- No export functionality for expense reports
- No multi-currency support (currently USD only)

### Planned Enhancements
- **Real OCR Integration**: Google Cloud Vision API or AWS Textract
- **Real-time Updates**: Live updates when trip members add expenses
- **Push Notifications**: Notify users of new expenses and settlement requests
- **Advanced Splitting**: Percentage-based splits, tax and tip handling
- **Expense Categories**: Categorize expenses (food, transport, accommodation)
- **Expense Reports**: Export trip summaries as PDF or CSV
- **Multi-currency**: Support for different currencies with exchange rates
- **Offline Support**: Cache data for offline viewing
- **Social Features**: Comments on expenses, expense photos
- **Advanced Analytics**: Spending patterns and insights

## Troubleshooting

### Common Issues

1. **"Network request failed" errors**
   - Check your Supabase URL and API key in `lib/supabase.js`
   - Ensure your internet connection is working
   - Verify Supabase project is active

2. **Camera not working**
   - Grant camera permissions when prompted
   - On iOS simulator, camera won't work (use device or gallery option)

3. **Database errors**
   - Ensure you've run the complete SQL schema from `database/schema.sql`
   - Check Supabase logs in the dashboard for detailed error messages

4. **Authentication issues**
   - Verify email confirmation if required
   - Check Supabase Auth settings in dashboard

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Test thoroughly on both iOS and Android
6. Submit a pull request with a clear description

## License

MIT License - see LICENSE file for details

## Support

For questions or issues:
- Create an issue on GitHub
- Check the troubleshooting section above
- Review Supabase documentation for database-related issues
