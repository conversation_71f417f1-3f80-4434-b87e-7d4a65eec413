{"name": "expense-tracker-mvp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.5", "@supabase/supabase-js": "^2.54.0", "expo": "~53.0.20", "expo-barcode-scanner": "^13.0.1", "expo-camera": "^16.1.11", "expo-document-picker": "^13.1.6", "expo-image-picker": "^16.1.4", "expo-status-bar": "~2.2.3", "qrcode-generator": "^2.0.4", "react": "19.0.0", "react-hook-form": "^7.62.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.28.0", "react-native-get-random-values": "^1.11.0", "react-native-paper": "^5.14.5", "react-native-qrcode-svg": "^6.3.15", "react-native-safe-area-context": "^5.6.0", "react-native-screens": "^4.13.1", "react-native-svg": "^15.12.1", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}