import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';

const supabaseUrl = 'https://jeezwqcsuzdcdzuxpgnk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImplZXp3cWNzdXpkY2R6dXhwZ25rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ3NzA0MzcsImV4cCI6MjA3MDM0NjQzN30.WLnMlkfSyc1Kj9SVuX82pcebCBUQuOQiVgVinjJ7qTg';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
